/*
 * Created Date: Tuesday, 16th July 2025, 16:00:00
 * Author: AI Assistant
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../repository/ticket_repo.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketFollowMembersUseCase extends GPBaseFutureUseCase<TicketFollowMembersInput, TicketFollowMembersOutput> {
  const TicketFollowMembersUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<TicketFollowMembersOutput> buildUseCase(
    TicketFollowMembersInput input,
  ) async {
    var params = {
      "user_ids": input.userIds?.join(","),
      "collab_ids": input.collabIds?.join(","),
      "department_ids": input.departmentIds?.join(","),
      "role_ids": input.roleIds?.join(","),
    };

    final response = await _ticketRepository.followMembers(
      ticketId: input.ticketId,
      params: params,
    );

    if (response.data != null && response.data?.isNotEmpty == true) {
      return TicketFollowMembersOutput(response.data ?? []);
    }

    return const TicketFollowMembersOutput([]);
  }
}

class TicketFollowMembersInput extends GPBaseInput {
  const TicketFollowMembersInput({
    required this.ticketId,
    this.userIds,
    this.collabIds,
    this.departmentIds,
    this.roleIds,
  });

  final String ticketId;
  final List<String>? userIds;
  final List<String>? collabIds;
  final List<String>? departmentIds;
  final List<String>? roleIds;
}

class TicketFollowMembersOutput extends GPBaseOutput {
  const TicketFollowMembersOutput(this.result);

  final List<Assignee> result;
}
