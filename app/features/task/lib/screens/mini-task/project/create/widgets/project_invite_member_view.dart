import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/screens/mini-task/project/create/project_create_controller.dart';

class ProjectInviteMemberView extends StatelessWidget {
  const ProjectInviteMemberView({
    super.key,
    required this.controller,
  });

  final ProjectCreateController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.taskProject_project_members.tr,
              style: textStyle(GPTypography.headingMedium)
                  ?.merge(TextStyle(color: GPColor.contentPrimary)),
            ),
            InkWell(
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
                controller.invite(controller.isCreating, controller.project);
              },
              child: Row(
                children: [
                  SvgWidget("assets/images/ic-add.svg",
                      width: 16,
                      height: 16,
                      color: GPColor.functionAccentPrimary),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.taskProject_add.tr,
                    style: textStyle(GPTypography.headingSmall)
                        ?.mergeColor(GPColor.functionAccentWorkSecondary),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _NoteView(title: LocaleKeys.taskProject_note1_invite_member.tr),
        _NoteView(title: LocaleKeys.taskProject_note2_invite_member.tr),
        _NoteView(title: LocaleKeys.taskProject_note3_invite_member.tr),
      ],
    ).paddingOnly(left: 12, right: 12, top: 40);
  }
}

class _NoteView extends StatelessWidget {
  final String title;
  const _NoteView({
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 6, right: 6, top: 10),
          width: 5,
          height: 5,
          decoration: BoxDecoration(
            color: GPColor.contentSecondary,
            shape: BoxShape.circle,
          ),
        ),
        Expanded(
          child: Text(
            title,
            style: textStyle(GPTypography.bodyMedium)
                ?.mergeColor(GPColor.contentSecondary),
          ),
        ),
      ],
    );
  }
}
