import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/secure_download/gp_secure_download.dart';
import 'package:gp_feat_task/models/comment/comment.dart';

import '../input/input_comment_edit_widget.dart';
import 'comment_base_widget.dart';
import 'input_comment_ext.dart';

// rule hiện tại, comment media chỉ hiển thị duy nhất 1 image/video
// TODO(noted by ToanNM): đang thấy duplicated code với gp_shared
/// sửa tạm trong task.
class CommentItemTypeMedia extends CommentItemBaseWidget {
  const CommentItemTypeMedia({
    required super.comment,
    required super.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (comment.isError) {
      return _CommentErrorView(
        key: key,
        comment: comment,
        controller: controller,
      );
    } else if (comment.isUploading) {
      return _CommentLoadingView(
        key: key,
        comment: comment,
        controller: controller,
      );
    } else if (comment.hasMedias) {
      return _CommentMediaSuccess(
        key: key,
        comment: comment,
        controller: controller,
      );
    } else {
      return const SizedBox();
    }
  }
}

class _CommentMediaSuccess extends CommentItemBaseWidget {
  const _CommentMediaSuccess({
    required super.comment,
    required super.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    bool isFile = comment.hasMedias &&
        !((comment.medias?.first.isImage ?? false) ||
            (comment.medias?.first.isVideo ?? false) ||
            (comment.medias?.first.isGif ?? false));
    if (isFile) {
      final media = comment.medias!.first;
      final mediaLocal = media.mediaLocal;
      final fileName = media.fileName ?? mediaLocal?.platformFile.name ?? '';
      final fileSize = Utils.readableFileSize(
          bytes: media.size ?? mediaLocal?.platformFile.size ?? 0);

      return InkWell(
        onTap: () {
          final mediaJson = media.toJson();
          mediaJson['downloadSource'] = DownloadSource(
            type: DownloadSourceIdType.comment,
            id: comment.id ?? "",
          ).toJson();
          mediaJson['mimeType'] = GMediaMimeType.file.name;
          mediaJson['fileName'] = fileName;
          Utils.openMediaPreview([GMedia.fromJson(mediaJson)]);
        },
        child: CommentFileWidget(
          fileName: fileName,
          fileSize: fileSize,
          margin: const EdgeInsets.only(top: 4),
          hasBgColor: false,
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: () => controller.viewMedia(comment),
        onLongPress: () => controller.onCommentTap(comment),
        child: Stack(
          alignment: Alignment.center,
          children: [
            ..._childrens(),
            if ((comment.medias?.first.isVideo ?? false) ||
                (comment.commentMediaLocals?.first.local?.isVideo ?? false))
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: GPColor.scrim,
                  border: Border.all(
                    color: GPColor.linePrimary,
                    width: 2,
                  ),
                ),
                child: SvgWidget(
                  "assets/images/ic24-fill-play.png",
                  width: 24,
                  height: 24,
                  fit: BoxFit.scaleDown,
                  color: GPColor.contentInversePrimary,
                ),
              ),
          ],
        ),
      ),
    ).paddingOnly(top: 4);
  }

  List<Widget> _childrens() {
    final Medias media = comment.medias!.first;

    List<Widget> childs = [];
    if (comment.hasMediaLocals) {
      childs.add(_mediaLocal());
    } else if (comment.hasMedias) {
      final imageUrl = Utils.imageThumb(media.thumbPattern ?? media.src,
          "${media.resizeWidth * 3}x${media.resizeHeight * 3}");
      childs.add(
        GPNetworkImage(
          url: imageUrl,
          width: media.resizeWidth.toDouble(),
          height: media.resizeHeight.toDouble(),
          placeholder: Container(
            width: media.resizeWidth.toDouble(),
            height: media.resizeHeight.toDouble(),
            color: GPColor.contentQuaternary,
          ),
          fit: BoxFit.cover,
          headers: GpSecureDownload.headers(
            imageUrl,
            sourceId: 'task_comment_media:${media.id}',
          ),
        ),
      );
    }
    return childs;
  }

  Widget _mediaLocal() {
    if (comment.commentMediaLocals == null ||
        comment.commentMediaLocals?.first.local == null) {
      return const SizedBox();
    }

    MediaLocal mediaLocal = comment.commentMediaLocals!.first.local!;
    return comment.commentMediaLocals?.first.local?.isImage ?? false
        ? Image.file(
            File(mediaLocal.platformFile.path!),
            height: mediaLocal.displayHeight.toDouble(),
            width: mediaLocal.displayWidth.toDouble(),
            fit: BoxFit.cover,
          )
        : UploadLocalThumbWidget(
            mediaLocal: mediaLocal,
            width: mediaLocal.displayWidth.toDouble(),
            height: mediaLocal.displayHeight.toDouble(),
          );
  }
}

class _CommentLoadingView extends CommentItemBaseWidget {
  const _CommentLoadingView({
    required super.comment,
    required super.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (comment.commentMediaLocals == null ||
        comment.commentMediaLocals!.isEmpty) {
      return const SizedBox();
    }

    MediaLocal? mediaLocal = comment.commentMediaLocals?.first.local;

    if (mediaLocal == null) {
      return const SizedBox();
    }

    if ((mediaLocal.uploadProgress.value) >= 100) {
      return const SizedBox();
    }

    if (mediaLocal.isFile) {
      return CommentFileWidget(
        fileName: mediaLocal.platformFile.name,
        fileSize: Utils.readableFileSize(bytes: mediaLocal.platformFile.size),
        prefixWidget: Obx(
          () {
            return mediaLocal.uploadProgress.value < 100
                ? InkWell(
                    onTap: mediaLocal.cancelToken.cancel,
                    customBorder: const CircleBorder(),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: GPColor.functionAccentWorkSecondary,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          CircularProgressIndicator(
                            color: GPColor.contentInversePrimary
                                .withAlpha((0.5 * 255).toInt()),
                            strokeWidth: 4,
                            value: mediaLocal.uploadProgress.value.toDouble(),
                          ).paddingAll(2),
                          const Icon(
                            Icons.close,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  )
                : SvgWidget(
                    fileExtensionType(fileName: mediaLocal.platformFile.name)
                        .iconPath,
                    // color: Color(0xFF8451D7),
                    width: 36,
                    height: 36,
                  );
          },
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      width: mediaLocal.loadingWidth.toDouble(),
      height: mediaLocal.loadingHeight.toDouble(),
      decoration: BoxDecoration(
        color: GPColor.bgSecondary,
        border: Border.all(color: GPColor.lineTertiary),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
          child: Obx(
        () => GPCircleIndicator(
          radius: 26,
          gradientColors: [
            GPColor.functionAccentWorkSecondary38,
            GPColor.functionAccentWorkSecondary
          ],
          onCancel: () {
            mediaLocal.cancelToken.cancel();
          },
          progress: mediaLocal.uploadProgress.value,
        ),
      )),
    );
  }
}

class _CommentErrorView extends CommentItemBaseWidget {
  const _CommentErrorView({
    required super.comment,
    required super.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    MediaLocal? mediaLocal = comment.commentMediaLocals?.first.local;

    return Container(
      margin: const EdgeInsets.only(top: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      width: mediaLocal?.displayWidth.toDouble(),
      height: mediaLocal?.displayHeight.toDouble(),
      decoration: BoxDecoration(
        color: GPColor.bgSecondary,
        border: Border.all(color: GPColor.lineTertiary),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SvgWidget("assets/images/ic24-fill-warningmark.png"),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  LocaleKeys.task_commentUploadError.tr,
                  style: textStyle(GPTypography.headingSmall)
                      ?.mergeColor(GPColor.contentSecondary),
                  textAlign: TextAlign.center,
                ),
              )
            ],
          ),
          const SizedBox(height: 12),
          GapoButton(
            titleStyle: textStyle(GPTypography.headingSmall)
                ?.mergeColor(GPColor.contentPrimary),
            text: LocaleKeys.task_commentUploadTryAgain.tr,
            backgroundColor: GPColor.bgSecondary,
            borderRadius: 8,
            width: 72,
            onPressed: _reUploadMedias,
          )
        ],
      ),
    );
  }

  void _reUploadMedias() {
    // if (Get.isRegistered<InputCommentController>()) {
    //   Get.find<InputCommentController>().sendComment(comment: comment);
    // }
    controller.sendComment(comment: comment);
  }
}
