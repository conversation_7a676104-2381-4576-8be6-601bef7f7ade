import 'package:gp_core/core.dart';
import 'package:gp_feat_task/screens/task_details/comment/medias/media.dart';

import 'mention_ext.dart';

export 'mention_ext.dart';

const updateNewMentionRuleMS = 1642561619;
//GMT: Wednesday, January 19, 2022 3:06:59 AM
//Your time zone: Wednesday, January 19, 2022 10:06:59 AM

class Comment {
  Comment({
    this.parentId,
    this.commentAs,
    this.text,
    this.mentions,
    this.medias,
    // this.sticker,
    // this.donate,
    // this.link,
    this.type,
    this.commentType,
    this.status,
    this.dataSource,
    this.id,
    this.targetId,
    this.targetType,
    this.replyCount,
    // this.totalReact,
    // this.reactCount,
    // this.reactStatus,
    this.comments,
    this.user,
    this.page,
    this.group,
    this.createdAt,
    this.updatedAt,
    this.replyHighLightComment,
  });

  bool canReply = true;

  String? parentId;
  late final CommentAs? commentAs;
  String? text;
  List<Mentions>? mentions;
  List<Medias>? medias;
  // late final Sticker? sticker;
  // late final Donate? donate;
  // late final Link? link;
  late final int? type;
  String? replyHighLightComment;

  /// Loại bình luận:
  /// 0: activity logs
  /// 1: text
  /// 2: image
  /// 3: sticker
  /// 4: donate
  /// 5: link
  late final int? commentType;
  late final int? status;

  /// dataSource:
  ///0: unspecified
  ///1: pc_web
  ///2: pc_mobile
  ///3: ios
  ///4: android
  ///5: crawl
  ///6: cms
  ///7: service
  late final int? dataSource;
  String? id;
  late final String? targetId;
  late final String? targetType;
  int? replyCount;
  // late final int? totalReact;
  // late final ReactCount? reactCount;
  // late final int? reactStatus;
  List<Comment>? comments;
  late final User? user;
  late final Page? page;
  late final Group? group;
  int? createdAt;
  int? updatedAt;

  // local variables, hiển thị loading && error khi comment medias
  List<CommentMediaModel>? commentMediaLocals;

  bool get isTypeActivityLogs => commentType == 0;

  bool get isTypeText => commentType == 1;

  bool get isTypeImage => commentType == 2;

  bool get hasMedias => medias != null && medias!.isNotEmpty;

  bool get hasText => (text ?? "").isNotEmpty;

  bool get isUploadingOrError => isUploading || isError;

  bool get hasMediaLocals =>
      commentMediaLocals != null && commentMediaLocals!.isNotEmpty;

  bool get isUploading => hasMediaLocals && !isError && !hasMedias;

  bool get isError =>
      hasMediaLocals &&
      commentMediaLocals!.first.local?.error != null &&
      !hasMedias;

  Rx<bool> rxUseHighlightBg = false.obs;

  Comment.fromJson(Map<String, dynamic> json) {
    parentId = json['parent_id'];
    commentAs = CommentAs.fromJson(json['comment_as']);
    text = json['text'];
    mentions = json['mentions'] == null
        ? null
        : List.from(json['mentions'])
            .map((e) => _applyNewMentionRule(json)
                ? Mentions.fromJsonNewRule(e)
                : Mentions.fromJson(e))
            .toList();
    medias = json['medias'] == null
        ? null
        : List.from(json['medias']).map((e) => Medias.fromJson(e)).toList();
    // sticker =
    //     json['sticker'] == null ? null : Sticker.fromJson(json['sticker']);
    // donate = json['donate'] == null ? null : Donate.fromJson(json['donate']);
    // link = json['link'] == null ? null : Link.fromJson(json['link']);
    type = json['type'];
    commentType = json['comment_type'];
    status = json['status'];
    dataSource = json['data_source'];
    id = json['id'];
    targetId = json['target_id'];
    targetType = json['target_type'];
    replyCount = json['reply_count'];
    // totalReact = json['total_react'];
    // reactCount = json['react_count'] == null
    //     ? null
    //     : ReactCount.fromJson(json['react_count']);
    // reactStatus = json['react_status'];
    comments = json['comments'] != null
        ? (json['comments']['data'] != null
            ? List<Comment>.from(
                json['comments']['data'].map((e) => Comment.fromJson(e)))
            : null)
        : null;
    user = json['user'] == null ? null : User.fromJson(json['user']);
    page = json['page'] == null ? null : Page.fromJson(json['page']);
    group = json['group'] == null ? null : Group.fromJson(json['group']);
    createdAt = ParserHelper.parseInt(json['created_at']);
    updatedAt = ParserHelper.parseInt(json['updated_at']);
  }

  /// áp dụng lại rule chuẩn của mention:
  /// offset: vị trí đầu tiên của mention: không sửa gì
  /// length: không còn là vị trí kết thúc của mention trên Mobile nữa. length (vị trí kết thúc mention) = offset + length
  bool _applyNewMentionRule(Map<String, dynamic> json) {
    int createdAt = ParserHelper.parseInt(json['created_at']) ?? 0;
    int updatedAt = ParserHelper.parseInt(json['updated_at']) ?? 0;
    int dataSource = json['data_source'] ?? 0;

    // với Mobile, áp dụng rule mới từ thời điểm update, với web và các dataSource thì luôn luôn
    if (dataSource == 3 || dataSource == 4) {
      return createdAt >= updateNewMentionRuleMS ||
          updatedAt >= updateNewMentionRuleMS;
    }
    return true;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['parent_id'] = parentId;
    data['comment_as'] = commentAs?.toJson();
    data['text'] = (text ?? "").trim();
    data['mentions'] = mentions?.map((e) => e.toJson()).toList();
    data['medias'] = medias?.map((e) => e.toJson()).toList();
    // data['sticker'] = sticker?.toJson();
    // data['donate'] = donate?.toJson();
    // data['link'] = link?.toJson();
    data['type'] = type;
    data['comment_type'] = commentType;
    data['status'] = status;
    data['data_source'] = dataSource;
    data['id'] = id;
    data['target_id'] = targetId;
    data['target_type'] = targetType;
    data['reply_count'] = replyCount;
    // data['total_react'] = totalReact;
    // data['react_count'] = reactCount?.toJson();
    // data['react_status'] = reactStatus;
    data['comments'] = comments;
    data['user'] = user?.toJson();
    data['page'] = page?.toJson();
    data['group'] = group?.toJson();
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }

  String get updatedAtDisplay {
    if (updatedAt == null) return 'Đã từ rất lâu rồi';
    return Utils.timeAgoString(updatedAt! * 1000);
  }

  bool get isEdited {
    return createdAt != updatedAt;
  }
}

class CommentAs {
  CommentAs({
    required this.authorType,
    required this.authorId,
  });

  late final String authorType;
  late final String authorId;

  CommentAs.fromJson(Map<String, dynamic> json) {
    authorType = json['author_type'];
    authorId = json['author_id'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['author_type'] = authorType;
    data['author_id'] = authorId;
    return data;
  }
}

class Mentions with MentionExt {
  Mentions({
    required this.offset,
    required this.length,
    required this.mentionId,
  });

  late int offset;
  late int length;
  late final String mentionId;

  Mentions.fromJson(Map<String, dynamic> json) {
    offset = json['offset'];
    length = json['length'];
    mentionId = json['mention_id'];
  }

  Mentions.fromJsonNewRule(Map<String, dynamic> json) {
    offset = json['offset'];
    length = json['length'] + offset;
    mentionId = json['mention_id'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (length > offset) {
      data['offset'] = offset;
      data['length'] = length - offset;
    } else {
      data['offset'] = offset;
      data['length'] = length;
    }
    data['mention_id'] = mentionId;
    return data;
  }
}

// max height
const commentDisplayMediaMaxHeight = 160;

class Medias {
  Medias({
    required this.id,
    required this.type,
    required this.width,
    required this.height,
    required this.src,
    // required this.thumbPattern,
    this.size,
    this.fileName,
    this.mediaLocal,
  }) {
    fileType = "";

    _countMediaSize();
  }

  late String id;

  late String type;
  late String?
      fileType; // sử dụng cho trường hợp upload video, fileType = "mp4", type = "file"
  late int width;
  late int height;
  late String src;
  String? thumbPattern;

  // khi hiển thị media: width, height của khung image/video, đã resize lại, hiển thị theo ratio, sử dụng tại client
  int resizeWidth = 0;
  int resizeHeight = 0;

  // khi edit media: width, height của khung image/video, đã resize lại, hiển thị theo ratio, sử dụng tại client
  late int editWidth;
  late int editHeight;

  int? size;

  String? fileName;

  bool get isImage => type == "image";

  bool get isVideo =>
      type == "video" ||
      (type == "file" && kVideoFileExtensions.contains(fileType));

  bool get isGif =>
      fileName?.substring(fileName?.lastIndexOf(".") ?? 0) == ".gif";

  // hiển thị thumb khi upload lên server, chưa fetch lại data
  MediaLocal? mediaLocal;

  Medias.fromJson(Map<String, dynamic> json) {
    if (kVideoFileExtensions.contains(json['type'])) {
      type = "file";
    } else {
      type = json['type'];
    }
    id = json["id"];
    fileType = json['file_type'] == null ? json['file_type'] : null;
    width = json['width'] ?? 0;
    height = json['height'] ?? 0;
    src = json['src'];
    thumbPattern = json['thumb_pattern'];

    // if (json['type'] == "image") {
    //   fileName = "$id${extension(src)}";
    // } else {
    fileName = json['name'];
    // }

    size = json['size'] ?? 0;

    _countMediaSize();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (kVideoFileExtensions.contains(fileType)) {
      /*
        Trước lấy theo fileType, BE đã update lại:
        Định dạng sẽ là:
        - Video: fileType="file", type="video"
      */
      data['type'] = "video"; // fileType;
    } else {
      data['type'] = type;
    }
    data['id'] = id;
    data['width'] = width;
    data['height'] = height;
    data['src'] = src;
    data['thumb_pattern'] = thumbPattern;
    data['file_type'] = fileType;
    data['size'] = size;
    data['name'] = fileName ?? mediaLocal?.platformFile.name;
    return data;
  }

  void _countMediaSize() {
    double ratio = width / height;
    if (ratio > commentMediaMaxRatio) {
      ratio = commentMediaMaxRatio;
    } else if (ratio != 1.0) {
      ratio = 2 / 3;
    }

    resizeHeight = commentDisplayMediaMaxHeight;
    resizeWidth = (resizeHeight * ratio).round();

    editHeight = commentPickMediaMaxHeight;
    editWidth = (editHeight * ratio).round();
  }
}

// class Sticker {
//   Sticker({
//     required this.type,
//     required this.stickerId,
//     required this.src,
//   });

//   late final String type;
//   late final String stickerId;
//   late final String src;

//   Sticker.fromJson(Map<String, dynamic> json) {
//     type = json['type'];
//     stickerId = json['sticker_id'];
//     src = json['src'];
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     data['type'] = type;
//     data['sticker_id'] = stickerId;
//     data['src'] = src;
//     return data;
//   }
// }

// class Donate {
//   Donate({
//     required this.amount,
//   });

//   late final String amount;

//   Donate.fromJson(Map<String, dynamic> json) {
//     amount = json['amount'];
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     data['amount'] = amount;
//     return data;
//   }
// }

// class Link {
//   Link({
//     required this.type,
//     required this.src,
//     required this.title,
//     required this.description,
//     required this.thumb,
//   });

//   late final String type;
//   late final String src;
//   late final String title;
//   late final String description;
//   late final Thumb? thumb;

//   Link.fromJson(Map<String, dynamic> json) {
//     type = json['type'];
//     src = json['src'];
//     title = json['title'];
//     description = json['description'];
//     thumb = json['thumb'] == null ? null : Thumb.fromJson(json['thumb']);
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     data['type'] = type;
//     data['src'] = src;
//     data['title'] = title;
//     data['description'] = description;
//     data['thumb'] = thumb?.toJson();
//     return data;
//   }
// }

// class Thumb {
//   Thumb({
//     required this.type,
//     required this.width,
//     required this.height,
//     required this.src,
//   });

//   late final String type;
//   late final int width;
//   late final int height;
//   late final String src;

//   Thumb.fromJson(Map<String, dynamic> json) {
//     type = json['type'];
//     width = json['width'];
//     height = json['height'];
//     src = json['src'];
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     data['type'] = type;
//     data['width'] = width;
//     data['height'] = height;
//     data['src'] = src;
//     return data;
//   }
// }

// class ReactCount {
//   ReactCount({
//     required this.reactType_1,
//     required this.reactType_2,
//     required this.reactType_3,
//     required this.reactType_4,
//     required this.reactType_5,
//     required this.reactType_6,
//   });

//   late final int reactType_1;
//   late final int reactType_2;
//   late final int reactType_3;
//   late final int reactType_4;
//   late final int reactType_5;
//   late final int reactType_6;

//   ReactCount.fromJson(Map<String, dynamic> json) {
//     reactType_1 = json['react_type_1'];
//     reactType_2 = json['react_type_2'];
//     reactType_3 = json['react_type_3'];
//     reactType_4 = json['react_type_4'];
//     reactType_5 = json['react_type_5'];
//     reactType_6 = json['react_type_6'];
//   }

//   Map<String, dynamic> toJson() {
//     final data = <String, dynamic>{};
//     data['react_type_1'] = reactType_1;
//     data['react_type_2'] = reactType_2;
//     data['react_type_3'] = reactType_3;
//     data['react_type_4'] = reactType_4;
//     data['react_type_5'] = reactType_5;
//     data['react_type_6'] = reactType_6;
//     return data;
//   }
// }

class User {
  User({
    required this.userId,
    required this.displayName,
    required this.fullName,
    this.cover,
    required this.avatar,
    required this.avatarThumbPattern,
    this.coverThumbPattern,
    this.gender,
    this.status,
    this.statusVerify,
    this.birthday,
  });

  late final String userId;
  late final String displayName;
  late final String? fullName;
  late final String? cover;
  late final String avatar;
  late final String avatarThumbPattern;
  late final String? coverThumbPattern;
  late final Gender? gender;
  late final int? status;
  late final int? statusVerify;
  late final Birthday? birthday;

  User.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    displayName = json['display_name'];
    fullName = json['full_name'] == null ? json['full_name'] : null;
    cover = json['cover'];
    avatar = json['avatar'] ?? '';
    avatarThumbPattern = json['avatar_thumb_pattern'] ?? '';
    coverThumbPattern = json['cover_thumb_pattern'];
    gender = json['gender'] == null ? null : Gender.fromJson(json['gender']);
    status = json['status'];
    statusVerify = json['status_verify'];
    birthday =
        json['birthday'] == null ? null : Birthday.fromJson(json['birthday']);
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_id'] = userId;
    data['display_name'] = displayName;
    data['full_name'] = fullName;
    data['cover'] = cover;
    data['avatar'] = avatar;
    data['avatar_thumb_pattern'] = avatarThumbPattern;
    data['cover_thumb_pattern'] = coverThumbPattern;
    data['gender'] = gender?.toJson();
    data['status'] = status;
    data['status_verify'] = statusVerify;
    data['birthday'] = birthday?.toJson();
    return data;
  }
}

class Gender {
  Gender({
    required this.gender,
    required this.privacy,
  });

  late final int gender;
  late final int privacy;

  Gender.fromJson(Map<String, dynamic> json) {
    gender = json['gender'];
    privacy = json['privacy'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['gender'] = gender;
    data['privacy'] = privacy;
    return data;
  }
}

class Birthday {
  Birthday({
    required this.day,
    required this.month,
    required this.year,
    required this.zodiac,
    required this.privacyBirthDate,
    required this.privacyBirthYear,
  });

  late final int day;
  late final int month;
  late final int year;
  late final String zodiac;
  late final int privacyBirthDate;
  late final int privacyBirthYear;

  Birthday.fromJson(Map<String, dynamic> json) {
    day = json['day'];
    month = json['month'];
    year = json['year'];
    zodiac = json['zodiac'];
    privacyBirthDate = json['privacy_birth_date'];
    privacyBirthYear = json['privacy_birth_year'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['day'] = day;
    data['month'] = month;
    data['year'] = year;
    data['zodiac'] = zodiac;
    data['privacy_birth_date'] = privacyBirthDate;
    data['privacy_birth_year'] = privacyBirthYear;
    return data;
  }
}

class Page {
  Page({
    required this.pageId,
    required this.createAt,
    required this.lastUpdate,
    required this.title,
    required this.alias,
    required this.description,
    required this.info,
    required this.type,
    required this.typeName,
    required this.avatar,
    required this.cover,
    required this.email,
    required this.phone,
    required this.website,
    required this.pageStatus,
    required this.statusVerify,
    required this.avatarThumbPattern,
    required this.coverThumbPattern,
  });

  late final int pageId;
  late final String createAt;
  late final String lastUpdate;
  late final String title;
  late final String alias;
  late final String description;
  late final String info;
  late final int type;
  late final String typeName;
  late final String avatar;
  late final String cover;
  late final String email;
  late final String phone;
  late final String website;
  late final int pageStatus;
  late final int statusVerify;
  late final String avatarThumbPattern;
  late final String coverThumbPattern;

  Page.fromJson(Map<String, dynamic> json) {
    pageId = json['page_id'];
    createAt = json['create_at'];
    lastUpdate = json['last_update'];
    title = json['title'];
    alias = json['alias'];
    description = json['description'];
    info = json['info'];
    type = json['type'];
    typeName = json['type_name'];
    avatar = json['avatar'];
    cover = json['cover'];
    email = json['email'];
    phone = json['phone'];
    website = json['website'];
    pageStatus = json['page_status'];
    statusVerify = json['status_verify'];
    avatarThumbPattern = json['avatar_thumb_pattern'];
    coverThumbPattern = json['cover_thumb_pattern'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['page_id'] = pageId;
    data['create_at'] = createAt;
    data['last_update'] = lastUpdate;
    data['title'] = title;
    data['alias'] = alias;
    data['description'] = description;
    data['info'] = info;
    data['type'] = type;
    data['type_name'] = typeName;
    data['avatar'] = avatar;
    data['cover'] = cover;
    data['email'] = email;
    data['phone'] = phone;
    data['website'] = website;
    data['page_status'] = pageStatus;
    data['status_verify'] = statusVerify;
    data['avatar_thumb_pattern'] = avatarThumbPattern;
    data['cover_thumb_pattern'] = coverThumbPattern;
    return data;
  }
}

class Group {
  Group({
    required this.id,
    required this.alias,
    required this.name,
    required this.description,
    required this.privacy,
    required this.blocked,
    required this.cover,
    required this.coverThumbPattern,
  });

  late final String id;
  late final String alias;
  late final String name;
  late final String description;
  late final int privacy;
  late final bool blocked;
  late final String cover;
  late final String coverThumbPattern;

  Group.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    alias = json['alias'];
    name = json['name'];
    description = json['description'];
    privacy = json['privacy'];
    blocked = json['blocked'];
    cover = json['cover'];
    coverThumbPattern = json['cover_thumb_pattern'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['alias'] = alias;
    data['name'] = name;
    data['description'] = description;
    data['privacy'] = privacy;
    data['blocked'] = blocked;
    data['cover'] = cover;
    data['cover_thumb_pattern'] = coverThumbPattern;
    return data;
  }
}
